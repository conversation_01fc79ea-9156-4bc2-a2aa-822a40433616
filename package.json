{"name": "@paddle/nextjs-starter-kit", "version": "0.1.0", "private": true, "engines": {"node": ">=20"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier --write --ignore-unknown .", "prettier:check": "prettier --check --ignore-unknown .", "lint-staged": "lint-staged", "test": "pnpm lint && pnpm prettier:check"}, "git": {"pre-commit": "lint-staged"}, "lint-staged": {"*": "prettier --write --ignore-unknown"}, "dependencies": {"@paddle/paddle-js": "^1.4.2", "@paddle/paddle-node-sdk": "^2.8.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "lodash.throttle": "^4.1.1", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/lodash.throttle": "^4.1.9", "@types/node": "^24.0.7", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vercel/git-hooks": "^1.0.0", "eslint": "^9.30.0", "eslint-config-next": "15.3.4", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "pnpm": {"overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}}