import {
  CustomerCreatedEvent,
  CustomerUpdatedEvent,
  EventEntity,
  EventName,
  SubscriptionCreatedEvent,
  SubscriptionUpdatedEvent,
} from '@paddle/paddle-node-sdk';
import { createClient } from '@/utils/supabase/server-internal';

export class ProcessWebhook {
  async processEvent(eventData: EventEntity) {
    console.log('🔄 ProcessWebhook.processEvent called');
    console.log('📋 Event type:', eventData.eventType);

    switch (eventData.eventType) {
      case EventName.SubscriptionCreated:
        console.log('🆕 Processing SubscriptionCreated event');
        await this.updateSubscriptionData(eventData);
        break;
      case EventName.SubscriptionUpdated:
        console.log('🔄 Processing SubscriptionUpdated event');
        await this.updateSubscriptionData(eventData);
        break;
      case EventName.CustomerCreated:
        console.log('👤 Processing CustomerCreated event');
        await this.updateCustomerData(eventData);
        break;
      case EventName.CustomerUpdated:
        console.log('👤 Processing CustomerUpdated event');
        await this.updateCustomerData(eventData);
        break;
      default:
        console.log('❓ Unknown event type:', eventData.eventType);
        console.log('📊 Event data:', JSON.stringify(eventData, null, 2));
        break;
    }
  }

  private async updateSubscriptionData(eventData: SubscriptionCreatedEvent | SubscriptionUpdatedEvent) {
    console.log('💳 updateSubscriptionData called');
    const supabase = await createClient();

    try {
      // Get the price ID from the subscription
      const priceId = eventData.data.items[0].price?.id ?? '';
      console.log('💰 Processing subscription webhook for price ID:', priceId);
      console.log('📊 Full subscription data:', JSON.stringify(eventData.data, null, 2));

      // Find the corresponding plan in your database
      console.log('🔍 Looking for plan with paddle_product_id:', priceId);
      const { data: plan, error: planError } = await supabase
        .from('plans')
        .select('id, interval, name')
        .eq('paddle_product_id', priceId)
        .single();

      console.log('📋 Plan query result:', { plan, planError });

      if (planError || !plan) {
        console.error(`❌ No plan found for price ID: ${priceId}`, planError);

        // Let's also check what plans exist in the database
        const { data: allPlans } = await supabase.from('plans').select('*');
        console.log('📋 All plans in database:', allPlans);
        return;
      }

      console.log('✅ Found plan:', plan);

      // Get customer details from Paddle to find the user
      console.log('👤 Getting customer details from Paddle for ID:', eventData.data.customerId);
      const { getPaddleInstance } = await import('@/utils/paddle/get-paddle-instance');
      const paddle = getPaddleInstance();
      const customer = await paddle.customers.get(eventData.data.customerId);

      console.log('👤 Customer data from Paddle:', JSON.stringify(customer, null, 2));

      if (!customer.email) {
        console.error(`❌ No email found for customer: ${eventData.data.customerId}`);
        return;
      }

      console.log('📧 Customer email:', customer.email);

      // Find the user by email
      console.log('🔍 Looking for profile with email:', customer.email);
      const { data: profile, error: profileError } = await supabase
        .from('profile')
        .select('user_id, email')
        .eq('email', customer.email)
        .single();

      console.log('👤 Profile query result:', { profile, profileError });

      if (profileError || !profile) {
        console.error(`❌ No profile found for email: ${customer.email}`, profileError);

        // Let's check what profiles exist
        const { data: allProfiles } = await supabase.from('profile').select('email, user_id');
        console.log('👥 All profiles in database:', allProfiles);
        return;
      }

      console.log('✅ Found profile:', profile);

      // Calculate end date based on subscription billing cycle
      console.log('📅 Calculating subscription dates...');
      const startDate = new Date(eventData.data.startedAt || eventData.data.createdAt);
      const endDate = new Date(startDate);

      console.log('📅 Start date:', startDate.toISOString());

      // Add billing period to start date
      const billingCycle = eventData.data.items[0].price?.billingCycle;
      console.log('🔄 Billing cycle:', billingCycle);

      if (billingCycle?.interval === 'month') {
        endDate.setMonth(endDate.getMonth() + (billingCycle.frequency || 1));
        console.log('📅 Monthly billing - End date:', endDate.toISOString());
      } else if (billingCycle?.interval === 'year') {
        endDate.setFullYear(endDate.getFullYear() + (billingCycle.frequency || 1));
        console.log('📅 Yearly billing - End date:', endDate.toISOString());
      } else {
        // For lifetime plans or unknown intervals, set end date far in the future
        endDate.setFullYear(endDate.getFullYear() + 100);
        console.log('📅 Lifetime/Unknown billing - End date:', endDate.toISOString());
      }

      // Upsert subscription data
      const subscriptionData = {
        user_id: profile.user_id,
        plan_id: plan.id,
        paddle_subscription_id: eventData.data.id,
        status: eventData.data.status,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        updated_at: new Date().toISOString(),
      };

      console.log('💾 Subscription data to insert/update:', subscriptionData);

      // Check if subscription already exists
      console.log('🔍 Checking if subscription already exists for Paddle ID:', eventData.data.id);
      const { data: existingSubscription, error: existingError } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('paddle_subscription_id', eventData.data.id)
        .single();

      console.log('🔍 Existing subscription check:', { existingSubscription, existingError });

      let result;
      if (existingSubscription) {
        // Update existing subscription
        console.log('🔄 Updating existing subscription...');
        result = await supabase
          .from('subscriptions')
          .update(subscriptionData)
          .eq('paddle_subscription_id', eventData.data.id)
          .select();
        console.log('🔄 Update result:', result);
      } else {
        // Insert new subscription
        console.log('🆕 Inserting new subscription...');
        result = await supabase.from('subscriptions').insert(subscriptionData).select();
        console.log('🆕 Insert result:', result);
      }

      if (result.error) {
        console.error('❌ Error upserting subscription:', result.error);
        console.error('❌ Error details:', JSON.stringify(result.error, null, 2));
        throw result.error;
      }

      console.log('✅ Successfully processed subscription webhook:', eventData.data.id);
      console.log('✅ Final subscription data:', result.data);
    } catch (error) {
      console.error('Error in updateSubscriptionData:', error);
      throw error;
    }
  }

  private async updateCustomerData(eventData: CustomerCreatedEvent | CustomerUpdatedEvent) {
    const supabase = await createClient();

    try {
      console.log('Processing customer webhook for email:', eventData.data.email);

      // Update or create profile based on customer data
      // Note: We only update existing profiles, we don't create new ones
      // New profiles should be created during user signup
      const { error } = await supabase
        .from('profile')
        .update({
          // You might want to extract more data from customer if available
          firstname: eventData.data.name?.split(' ')[0] || null,
          lastname: eventData.data.name?.split(' ').slice(1).join(' ') || null,
        })
        .eq('email', eventData.data.email)
        .select();

      if (error) {
        console.error('Error updating customer profile:', error);
        // Don't throw error for customer updates as they're not critical
      } else {
        console.log('Successfully processed customer webhook:', eventData.data.id);
      }
    } catch (error) {
      console.error('Error in updateCustomerData:', error);
      // Don't throw error for customer updates as they're not critical
    }
  }
}
