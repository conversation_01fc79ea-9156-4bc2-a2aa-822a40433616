import { createClient } from '@/utils/supabase/client';
import { Subscription, Plan, Profile } from '@/lib/database.types';

// Client-side subscription utilities
export class SubscriptionClient {
  private supabase;

  constructor() {
    this.supabase = createClient();
  }

  // Get current user's subscriptions with plan details
  async getUserSubscriptions() {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user?.id) {
        return { data: [], error: 'User not authenticated' };
      }

      const { data: subscriptions, error } = await this.supabase
        .from('subscriptions')
        .select(`
          *,
          plans:plan_id (
            id,
            name,
            paddle_product_id,
            interval,
            price,
            is_active,
            discription
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching user subscriptions:', error);
        return { data: [], error: error.message };
      }

      return { data: subscriptions || [], error: null };
    } catch (err) {
      console.error('Error in getUserSubscriptions:', err);
      return { data: [], error: 'Failed to fetch subscriptions' };
    }
  }

  // Get a specific subscription by ID
  async getSubscriptionById(subscriptionId: string) {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user?.id) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data: subscription, error } = await this.supabase
        .from('subscriptions')
        .select(`
          *,
          plans:plan_id (
            id,
            name,
            paddle_product_id,
            interval,
            price,
            is_active,
            discription
          )
        `)
        .eq('id', subscriptionId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching subscription:', error);
        return { data: null, error: error.message };
      }

      return { data: subscription, error: null };
    } catch (err) {
      console.error('Error in getSubscriptionById:', err);
      return { data: null, error: 'Failed to fetch subscription' };
    }
  }

  // Get user's active subscription (most recent active one)
  async getUserActiveSubscription() {
    const { data: subscriptions, error } = await this.getUserSubscriptions();
    
    if (error || !subscriptions.length) {
      return { data: null, error: error || 'No subscriptions found' };
    }

    // Find the most recent active subscription
    const activeSubscription = subscriptions.find(sub => 
      sub.status === 'active' && 
      new Date(sub.end_date) > new Date()
    );

    if (!activeSubscription) {
      // If no active subscription, return the most recent one
      return { data: subscriptions[0], error: null };
    }

    return { data: activeSubscription, error: null };
  }

  // Check if user has any active subscriptions
  async hasActiveSubscription() {
    const { data: subscription } = await this.getUserActiveSubscription();
    return subscription && 
           subscription.status === 'active' && 
           new Date(subscription.end_date) > new Date();
  }

  // Get user profile
  async getUserProfile() {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user?.id) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data: profile, error } = await this.supabase
        .from('profile')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return { data: null, error: error.message };
      }

      return { data: profile, error: null };
    } catch (err) {
      console.error('Error in getUserProfile:', err);
      return { data: null, error: 'Failed to fetch profile' };
    }
  }

  // Get all active plans
  async getActivePlans() {
    try {
      const { data: plans, error } = await this.supabase
        .from('plans')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (error) {
        console.error('Error fetching plans:', error);
        return { data: [], error: error.message };
      }

      return { data: plans || [], error: null };
    } catch (err) {
      console.error('Error in getActivePlans:', err);
      return { data: [], error: 'Failed to fetch plans' };
    }
  }

  // Update subscription status
  async updateSubscriptionStatus(subscriptionId: string, status: string, cancelAt?: Date) {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user?.id) {
        return { error: 'User not authenticated' };
      }

      const updateData: any = {
        status: status,
        updated_at: new Date().toISOString(),
      };

      if (cancelAt) {
        updateData.cancel_at = cancelAt.toISOString();
      }

      const { data, error } = await this.supabase
        .from('subscriptions')
        .update(updateData)
        .eq('id', subscriptionId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating subscription:', error);
        return { error: error.message };
      }

      return { data, error: null };
    } catch (err) {
      console.error('Error in updateSubscriptionStatus:', err);
      return { error: 'Failed to update subscription' };
    }
  }

  // Listen to subscription changes
  subscribeToSubscriptionChanges(userId: string, callback: (payload: any) => void) {
    return this.supabase
      .channel('subscription-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'subscriptions',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }

  // Create a subscription (for manual testing or free plan assignment)
  async createSubscription(planId: string, status: string = 'active') {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user?.id) {
        return { error: 'User not authenticated' };
      }

      // Get plan details to calculate end date
      const { data: plan, error: planError } = await this.supabase
        .from('plans')
        .select('*')
        .eq('id', planId)
        .single();

      if (planError || !plan) {
        return { error: 'Plan not found' };
      }

      // Calculate end date based on plan interval
      const startDate = new Date();
      const endDate = new Date(startDate);
      
      switch (plan.interval) {
        case 'monthly':
          endDate.setMonth(endDate.getMonth() + 1);
          break;
        case 'yearly':
          endDate.setFullYear(endDate.getFullYear() + 1);
          break;
        case 'lifetime':
          endDate.setFullYear(endDate.getFullYear() + 100); // Far future for lifetime
          break;
      }

      const { data: subscription, error } = await this.supabase
        .from('subscriptions')
        .insert({
          user_id: user.id,
          plan_id: planId,
          status: status,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating subscription:', error);
        return { error: error.message };
      }

      return { data: subscription, error: null };
    } catch (err) {
      console.error('Error in createSubscription:', err);
      return { error: 'Failed to create subscription' };
    }
  }
}

// Export a singleton instance
export const subscriptionClient = new SubscriptionClient();
