.background-base {
  min-height: 1400px;
  width: 100%;
  position: absolute;
  z-index: -1;
}

.grid-bg {
  background: url('/assets/background/grid-bg.svg') no-repeat;
}

.grain-background {
  background: url('/assets/background/grain-bg.svg') repeat;
}

.grain-blur {
  top: -220px;
  background: url('/assets/background/grain-blur.svg') no-repeat 50%;
}

.large-blur {
  left: -30px;
  top: -864px;
  border-radius: 750px;
  opacity: 0.2;
  background: radial-gradient(
    70.71% 70.71% at 50% 50%,
    rgba(117, 173, 255, 0.2) 0%,
    rgba(117, 173, 255, 0) 70%,
    rgba(117, 173, 255, 0) 100%
  );
}

.small-blur {
  background: url('/assets/background/small-blur.svg') no-repeat 50%;
}

.featured-card-badge {
  position: relative;
  background: linear-gradient(90deg, #fff800 0%, #fffecc 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.featured-card-badge::before {
  content: '';
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 15%,
    rgba(255, 248, 0, 0.6) 50%,
    rgba(255, 255, 255, 0) 85%
  );
  position: absolute;
  left: 8px;
  top: -1px;
  width: 48px;
  height: 1px;
}
.pricing-card-border {
  position: relative;
}
.pricing-card-border::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px 16px 0 0;
  padding: 1px 1px 0;
  background: linear-gradient(180deg, #414b4e 49.5%, rgba(65, 75, 78, 0) 100%);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.footer-border {
  position: relative;
  background: linear-gradient(90deg, rgba(65, 75, 78, 0) 0%, #414b4e 49.5%, rgba(65, 75, 78, 0) 100%);
}
.footer-border::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: calc(50% - 124px);
  width: 248px;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 15%,
    rgba(255, 248, 0, 0.6) 50%,
    rgba(255, 255, 255, 0) 85%
  );
}

.featured-price-title {
  position: relative;
}
.featured-price-title::before {
  content: '';
  position: absolute;
  left: 44px;
  top: -7px;
  height: 17px;
  width: 296px;
  border-radius: 296px;
  opacity: 0.2;
  background: #fddd35;
  filter: blur(32px);
}

.featured-price-title::after {
  content: '';
  width: 196px;
  height: 4px;
  position: absolute;
  left: 94px;
  top: -2px;
  border-radius: 196px;
  opacity: 0.5;
  background: #4d94ff;
  filter: blur(12px);
}

.featured-hard-blur-bg {
  width: 88px;
  height: 4px;
  position: absolute;
  left: 50%;
  margin-left: -44px;
  top: -2px;
  background: #fff800;
  opacity: 0.5;
  filter: blur(12px);
}

.featured-yellow-highlight-bg {
  content: '';
  position: absolute;
  left: 50%;
  margin-left: -124px;
  width: 248px;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 15%,
    rgba(255, 248, 0, 0.6) 50%,
    rgba(255, 255, 255, 0) 85%
  );
}
.featured-vertical-hard-blur-bg {
  position: absolute;
  top: -140px;
  left: 50%;
  margin-left: -64px;
  width: 128px;
  height: 280px;
  border-radius: 280px;
  opacity: 0.1;
  background: #fff800;
  filter: blur(48px);
}

.featured-soft-blur-bg {
  position: absolute;
  top: -19px;
  left: 50%;
  margin-left: -192px;
  width: 384px;
  height: 37px;
  border-radius: 384px;
  opacity: 0.3;
  background: #fff800;
  filter: blur(32px);
}
