'use client';

import { createClient } from '@/utils/supabase/client';
import { useUserInfo } from '@/hooks/useUserInfo';
import { useAutoCountryDetection } from '@/hooks/useAutoCountryDetection';
import '../../styles/home-page.css';
import { HeroSection } from '@/components/home/<USER>/hero-section';
import { Pricing } from '@/components/home/<USER>/pricing';
import Header from './header/header';

export function HomePage() {
  const supabase = createClient();
  const { user } = useUserInfo(supabase);
  const { country, loading: countryLoading } = useAutoCountryDetection();

  // Log when country detection is complete (for debugging)
  if (!countryLoading) {
    console.log('🌍 Automatically detected country for pricing:', country);
  }

  return (
    <>

      <div>
        {/* <HomePageBackground /> */}
        <Header user={user} />
        <HeroSection />
        <Pricing country={country} />
        {/* <Footer /> */}
      </div>
    </>
  );
}
