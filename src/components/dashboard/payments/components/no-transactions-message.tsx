import { Card, CardContent } from '@/components/ui/card';
import { CreditCard } from 'lucide-react';

interface Props {
  message?: string;
}

export function NoTransactionsMessage({ message }: Props) {
  const defaultMessage = "No Paddle transactions found. This could mean you don't have any Paddle payments yet, or your subscription was created directly in the database.";
  
  return (
    <Card className="bg-background/50 backdrop-blur-[24px] border-border p-6">
      <CardContent className="flex flex-col items-center justify-center py-8 text-center">
        <CreditCard className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No Transactions Found</h3>
        <p className="text-muted-foreground max-w-md">
          {message || defaultMessage}
        </p>
      </CardContent>
    </Card>
  );
}
