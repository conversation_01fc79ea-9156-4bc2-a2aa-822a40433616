'use client';

import { getTransactions, getAllUserTransactions } from '@/utils/paddle/get-transactions';
import { ErrorContent } from '@/components/dashboard/layout/error-content';
import { DataTable } from '@/components/dashboard/payments/components/data-table';
import { columns } from '@/components/dashboard/payments/components/columns';
import { useEffect, useState } from 'react';
import { LoadingScreen } from '@/components/dashboard/layout/loading-screen';
import { usePagination } from '@/hooks/usePagination';
import { TransactionResponse } from '@/lib/api.types';
import { NoTransactionsMessage } from '@/components/dashboard/payments/components/no-transactions-message';

interface Props {
  subscriptionId?: string;
}

export function PaymentsContent({ subscriptionId }: Props) {
  const { after, goToNextPage, goToPrevPage, hasPrev } = usePagination();

  const [transactionResponse, setTransactionResponse] = useState<TransactionResponse>({
    data: [],
    hasMore: false,
    totalRecords: 0,
    error: undefined,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    (async () => {
      setLoading(true);
      // Use getAllUserTransactions if no subscriptionId, otherwise get transactions for specific subscription
      const response = subscriptionId
        ? await getTransactions(subscriptionId, after)
        : await getAllUserTransactions(after);
      if (response) {
        setTransactionResponse(response);
      }
      setLoading(false);
    })();
  }, [subscriptionId, after]);

  if (loading) {
    return <LoadingScreen />;
  }

  if (transactionResponse.error) {
    return <NoTransactionsMessage message={transactionResponse.error} />;
  }

  const { data: transactionData, hasMore, totalRecords } = transactionResponse;

  // Show helpful message if no transactions found
  if (!transactionData || transactionData.length === 0) {
    return <NoTransactionsMessage />;
  }

  return (
    <div>
      <DataTable
        columns={columns}
        hasMore={hasMore}
        totalRecords={totalRecords}
        goToNextPage={goToNextPage}
        goToPrevPage={goToPrevPage}
        hasPrev={hasPrev}
        data={transactionData}
      />
    </div>
  );
}
