import { DashboardUsageCardGroup } from '@/components/dashboard/landing/components/dashboard-usage-card-group';
import { DashboardSubscriptionCardGroup } from '@/components/dashboard/landing/components/dashboard-subscription-card-group';
import { DashboardTutorialCard } from '@/components/dashboard/landing/components/dashboard-tutorial-card';
import { DashboardTeamMembersCard } from '@/components/dashboard/landing/components/dashboard-team-members-card';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

function SubscriptionCardSkeleton() {
  return (
    <div className={'bg-background/50 backdrop-blur-[24px] border-border p-6 rounded-lg border'}>
      <div className="flex justify-between items-center pb-6 border-border border-b">
        <Skeleton className="h-6 w-40 bg-border" />
        <Skeleton className="h-8 w-20 bg-border" />
      </div>
      <div className="pt-6">
        <Skeleton className="h-20 w-full bg-border" />
      </div>
    </div>
  );
}

export function DashboardLandingPage() {
  return (
    <div className={'grid flex-1 items-start gap-6 p-0 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3'}>
      <div className={'grid auto-rows-max items-start gap-6 lg:col-span-2'}>
        <DashboardUsageCardGroup />
        <Suspense fallback={<SubscriptionCardSkeleton />}>
          <DashboardSubscriptionCardGroup />
        </Suspense>
      </div>
      <div className={'grid auto-rows-max items-start gap-6'}>
        <DashboardTeamMembersCard />
        <DashboardTutorialCard />
      </div>
    </div>
  );
}
