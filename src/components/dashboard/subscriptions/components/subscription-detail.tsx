'use client';

import { useSubscription } from '@/hooks/useSubscriptions';
import { SubscriptionPastPaymentsCard } from '@/components/dashboard/subscriptions/components/subscription-past-payments-card';
import { SubscriptionNextPaymentCard } from '@/components/dashboard/subscriptions/components/subscription-next-payment-card';
import { SubscriptionLineItems } from '@/components/dashboard/subscriptions/components/subscription-line-items';
import { SubscriptionHeader } from '@/components/dashboard/subscriptions/components/subscription-header';
import { Separator } from '@/components/ui/separator';
import { ErrorContent } from '@/components/dashboard/layout/error-content';
import { LoadingScreen } from '@/components/dashboard/layout/loading-screen';
import { useEffect, useState } from 'react';
import { getTransactions } from '@/utils/paddle/get-transactions';
import { Transaction } from '@paddle/paddle-node-sdk';

interface Props {
  subscriptionId: string;
}

export function SubscriptionDetail({ subscriptionId }: Props) {
  const { subscription, loading, error } = useSubscription(subscriptionId);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(false);

  // Fetch Paddle transactions when subscription data is available
  useEffect(() => {
    const fetchTransactions = async () => {
      if (subscription?.paddle_subscription_id) {
        setTransactionsLoading(true);
        try {
          const response = await getTransactions(subscription.paddle_subscription_id);
          if (response.data) {
            setTransactions(response.data);
          }
        } catch (error) {
          console.error('Error fetching transactions:', error);
        } finally {
          setTransactionsLoading(false);
        }
      }
    };

    fetchTransactions();
  }, [subscription?.paddle_subscription_id]);

  if (loading) {
    return <LoadingScreen />;
  }

  if (error || !subscription) {
    return <ErrorContent />;
  }

  // Create a Paddle-like object from our database data for compatibility with existing components
  const displaySubscription = {
    id: subscription.paddle_subscription_id || subscription.id,
    status: subscription.status,
    items: [
      {
        price: {
          name: subscription.plans?.name || 'Unknown Plan',
          description: subscription.plans?.discription || '',
        },
        quantity: 1,
        product: {
          name: subscription.plans?.name || 'Unknown Plan',
          description: subscription.plans?.discription || '',
          imageUrl: null, // We don't have product images in our schema
        },
      },
    ],
    currencyCode: 'USD', // You might want to store this in your plans table
    billingCycle: {
      interval:
        subscription.plans?.interval === 'monthly'
          ? 'month'
          : subscription.plans?.interval === 'yearly'
            ? 'year'
            : 'lifetime',
      frequency: 1,
    },
    startedAt: subscription.start_date,
    nextBilledAt: subscription.end_date,
    nextTransaction: {
      details: {
        totals: {
          total: ((subscription.plans?.price || 0) * 100).toString(), // Convert to cents
        },
      },
    },
    recurringTransactionDetails: {
      lineItems: [
        {
          priceId: subscription.plans?.paddle_product_id || '',
          quantity: 1,
          taxRate: '0', // Default to 0% tax rate since we don't store this
          product: {
            name: subscription.plans?.name || 'Unknown Plan',
            description: subscription.plans?.discription || '',
            imageUrl: null, // We don't have product images in our schema
          },
          totals: {
            subtotal: ((subscription.plans?.price || 0) * 100).toString(), // Convert to cents
            tax: '0', // Default to 0 tax since we don't store this
            total: ((subscription.plans?.price || 0) * 100).toString(), // Convert to cents
          },
        },
      ],
      totals: {
        subtotal: ((subscription.plans?.price || 0) * 100).toString(), // Convert to cents
        tax: '0', // Default to 0 tax since we don't store this
        total: ((subscription.plans?.price || 0) * 100).toString(), // Convert to cents
      },
    },
    scheduledChange: null, // We don't track scheduled changes yet
  };

  return (
    <>
      <div>
        <SubscriptionHeader subscription={displaySubscription} />
        <Separator className={'relative bg-border mb-8 dashboard-header-highlight'} />
      </div>
      <div className={'grid gap-6 grid-cols-1 xl:grid-cols-6'}>
        <div className={'grid auto-rows-max gap-6 grid-cols-1 xl:col-span-2'}>
          <SubscriptionNextPaymentCard transactions={transactions} subscription={displaySubscription} />
          <SubscriptionPastPaymentsCard transactions={transactions} subscriptionId={subscriptionId} />
        </div>
        <div className={'grid auto-rows-max gap-6 grid-cols-1 xl:col-span-4'}>
          <SubscriptionLineItems subscription={displaySubscription} />
        </div>
      </div>
    </>
  );
}
