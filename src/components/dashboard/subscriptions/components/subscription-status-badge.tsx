'use client';

import { useSubscriptionStatus } from '@/hooks/useSubscriptions';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-react';

export function SubscriptionStatusBadge() {
  const { hasActive, loading } = useSubscriptionStatus();

  if (loading) {
    return (
      <Badge variant="outline" className="gap-2">
        <Loader2 className="h-3 w-3 animate-spin" />
        Checking...
      </Badge>
    );
  }

  return (
    <Badge variant={hasActive ? 'default' : 'secondary'}>
      {hasActive ? 'Active Subscription' : 'No Active Subscription'}
    </Badge>
  );
}

// Example usage in any component:
// import { SubscriptionStatusBadge } from '@/components/dashboard/subscriptions/components/subscription-status-badge';
// 
// function MyComponent() {
//   return (
//     <div>
//       <h1>Dashboard</h1>
//       <SubscriptionStatusBadge />
//     </div>
//   );
// }
