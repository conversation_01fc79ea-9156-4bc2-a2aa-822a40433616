'use client';

import { SubscriptionDetail } from '@/components/dashboard/subscriptions/components/subscription-detail';
import { NoSubscriptionView } from '@/components/dashboard/subscriptions/views/no-subscription-view';
import { MultipleSubscriptionsView } from '@/components/dashboard/subscriptions/views/multiple-subscriptions-view';
import { SubscriptionErrorView } from '@/components/dashboard/subscriptions/views/subscription-error-view';
import { LoadingScreen } from '@/components/dashboard/layout/loading-screen';
import { useSubscriptions } from '@/hooks/useSubscriptions';

export function Subscriptions() {
  const { subscriptions, loading, error } = useSubscriptions();

  if (loading) {
    return <LoadingScreen />;
  }

  if (error) {
    return <SubscriptionErrorView />;
  }

  if (subscriptions.length === 0) {
    return <NoSubscriptionView />;
  } else if (subscriptions.length === 1) {
    return <SubscriptionDetail subscriptionId={subscriptions[0].id} />;
  } else {
    // Convert our subscription format to match what MultipleSubscriptionsView expects
    const paddleFormatSubscriptions = subscriptions.map((sub) => ({
      id: sub.id,
      status: sub.status,
      items: [
        {
          price: {
            name: sub.plans?.name || 'Unknown Plan',
            description: sub.plans?.discription || '',
          },
        },
      ],
      startedAt: sub.start_date,
      nextBilledAt: sub.end_date,
    }));

    return <MultipleSubscriptionsView subscriptions={paddleFormatSubscriptions} />;
  }
}
