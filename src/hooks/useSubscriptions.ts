import { useEffect, useState } from 'react';
import { subscriptionClient } from '@/utils/supabase/subscriptions-client';
import { Subscription, Plan } from '@/lib/database.types';
import { createClient } from '@/utils/supabase/client';

type SubscriptionWithPlan = Subscription & { plans: Plan };

export function useSubscriptions() {
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscriptions = async () => {
    setLoading(true);
    setError(null);
    
    const { data, error: fetchError } = await subscriptionClient.getUserSubscriptions();
    
    if (fetchError) {
      setError(fetchError);
    } else {
      setSubscriptions(data);
    }
    
    setLoading(false);
  };

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  return {
    subscriptions,
    loading,
    error,
    refetch: fetchSubscriptions,
  };
}

export function useSubscription(subscriptionId: string) {
  const [subscription, setSubscription] = useState<SubscriptionWithPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscription = async () => {
    if (!subscriptionId) return;
    
    setLoading(true);
    setError(null);
    
    const { data, error: fetchError } = await subscriptionClient.getSubscriptionById(subscriptionId);
    
    if (fetchError) {
      setError(fetchError);
    } else {
      setSubscription(data);
    }
    
    setLoading(false);
  };

  useEffect(() => {
    fetchSubscription();
  }, [subscriptionId]);

  return {
    subscription,
    loading,
    error,
    refetch: fetchSubscription,
  };
}

export function useActiveSubscription() {
  const [subscription, setSubscription] = useState<SubscriptionWithPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchActiveSubscription = async () => {
    setLoading(true);
    setError(null);
    
    const { data, error: fetchError } = await subscriptionClient.getUserActiveSubscription();
    
    if (fetchError) {
      setError(fetchError);
    } else {
      setSubscription(data);
    }
    
    setLoading(false);
  };

  useEffect(() => {
    fetchActiveSubscription();
  }, []);

  return {
    subscription,
    loading,
    error,
    refetch: fetchActiveSubscription,
  };
}

export function useSubscriptionStatus() {
  const [hasActive, setHasActive] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkStatus = async () => {
      setLoading(true);
      const isActive = await subscriptionClient.hasActiveSubscription();
      setHasActive(isActive);
      setLoading(false);
    };

    checkStatus();
  }, []);

  return { hasActive, loading };
}

// Hook for real-time subscription updates
export function useSubscriptionUpdates() {
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithPlan[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();
    
    // Initial fetch
    const fetchInitialData = async () => {
      const { data } = await subscriptionClient.getUserSubscriptions();
      setSubscriptions(data);
      setLoading(false);
    };

    fetchInitialData();

    // Set up real-time subscription
    const { data: { user } } = supabase.auth.getUser().then(({ data }) => {
      if (data.user?.id) {
        const channel = subscriptionClient.subscribeToSubscriptionChanges(
          data.user.id,
          (payload) => {
            console.log('Subscription change detected:', payload);
            // Refetch subscriptions when changes occur
            fetchInitialData();
          }
        );

        return () => {
          supabase.removeChannel(channel);
        };
      }
    });

    return () => {
      // Cleanup will be handled by the inner function
    };
  }, []);

  return { subscriptions, loading };
}

// Hook for managing subscription actions
export function useSubscriptionActions() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateStatus = async (subscriptionId: string, status: string, cancelAt?: Date) => {
    setLoading(true);
    setError(null);

    const { error: updateError } = await subscriptionClient.updateSubscriptionStatus(
      subscriptionId,
      status,
      cancelAt
    );

    if (updateError) {
      setError(updateError);
    }

    setLoading(false);
    return !updateError;
  };

  const createSubscription = async (planId: string, status: string = 'active') => {
    setLoading(true);
    setError(null);

    const { data, error: createError } = await subscriptionClient.createSubscription(planId, status);

    if (createError) {
      setError(createError);
    }

    setLoading(false);
    return { data, error: createError };
  };

  return {
    updateStatus,
    createSubscription,
    loading,
    error,
  };
}
