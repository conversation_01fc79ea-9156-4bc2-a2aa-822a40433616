import { NextRequest } from 'next/server';
import { createClient } from '@/utils/supabase/server-internal';

export async function GET(request: NextRequest) {
  console.log('🧪 Testing database connection and tables...');
  
  try {
    const supabase = await createClient();
    
    // Test 1: Check if plans table exists and has data
    console.log('📋 Checking plans table...');
    const { data: plans, error: plansError } = await supabase
      .from('plans')
      .select('*')
      .limit(5);
    
    console.log('📋 Plans result:', { plans, plansError });
    
    // Test 2: Check if profile table exists
    console.log('👤 Checking profile table...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profile')
      .select('*')
      .limit(5);
    
    console.log('👤 Profiles result:', { profiles, profilesError });
    
    // Test 3: Check if subscriptions table exists
    console.log('💳 Checking subscriptions table...');
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(5);
    
    console.log('💳 Subscriptions result:', { subscriptions, subscriptionsError });
    
    // Test 4: Check current user
    console.log('🔐 Checking current user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('🔐 User result:', { user: user?.email, userError });
    
    return Response.json({
      success: true,
      tables: {
        plans: { count: plans?.length || 0, error: plansError?.message },
        profiles: { count: profiles?.length || 0, error: profilesError?.message },
        subscriptions: { count: subscriptions?.length || 0, error: subscriptionsError?.message },
      },
      user: user?.email || 'No user',
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('💥 Database test error:', error);
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
