import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('🧪 Webhook test endpoint called');
  
  return Response.json({
    message: 'Webhook test endpoint is working!',
    timestamp: new Date().toISOString(),
    url: request.url,
    method: request.method,
  });
}

export async function POST(request: NextRequest) {
  console.log('🔔 Test webhook POST received!');
  console.log('📝 Headers:', Object.fromEntries(request.headers.entries()));
  
  const body = await request.text();
  console.log('📝 Body length:', body.length);
  console.log('📝 Body preview:', body.substring(0, 200));
  
  return Response.json({
    message: 'Test webhook POST received!',
    timestamp: new Date().toISOString(),
    bodyLength: body.length,
    headers: Object.fromEntries(request.headers.entries()),
  });
}
