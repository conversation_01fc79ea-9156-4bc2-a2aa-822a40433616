import { NextRequest } from 'next/server';
import { ProcessWebhook } from '@/utils/paddle/process-webhook';
import { getPaddleInstance } from '@/utils/paddle/get-paddle-instance';

const webhookProcessor = new ProcessWebhook();

export async function POST(request: NextRequest) {
  const signature = request.headers.get('paddle-signature') || '';
  const rawRequestBody = await request.text();
  const privateKey = process.env['PADDLE_NOTIFICATION_WEBHOOK_SECRET'] || '';

  console.log('🔔 Webhook received!');
  console.log('📝 Signature present:', !!signature);
  console.log('📝 Body length:', rawRequestBody.length);
  console.log('📝 Private key present:', !!privateKey);

  try {
    if (!signature || !rawRequestBody) {
      console.log('❌ Missing signature or body');
      return Response.json({ error: 'Missing signature from header' }, { status: 400 });
    }

    const paddle = getPaddleInstance();
    console.log('🏓 Paddle instance created');

    const eventData = await paddle.webhooks.unmarshal(rawRequestBody, privateKey, signature);
    const eventName = eventData?.eventType ?? 'Unknown event';

    console.log('🎯 Event type:', eventName);
    console.log('📊 Event data:', JSON.stringify(eventData, null, 2));

    if (eventData) {
      console.log('🔄 Processing event...');
      await webhookProcessor.processEvent(eventData);
      console.log('✅ Event processed successfully');
    } else {
      console.log('❌ No event data received');
    }

    return Response.json({ status: 200, eventName });
  } catch (e) {
    console.error('💥 Webhook error:', e);
    console.error('💥 Error details:', JSON.stringify(e, null, 2));
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}
