
import '../../../styles/checkout.css';
import { CheckoutContents } from '@/components/checkout/checkout-contents';
import { createClient } from '@/utils/supabase/server';
import Header from '../header';

export default async function CheckoutPage() {
  const supabase = await createClient();
  const { data } = await supabase.auth.getUser();
  return (
    <>
      <Header/> 
    <div>
      <CheckoutContents userEmail={data.user?.email} />
      </div>
      </>
  );
}
