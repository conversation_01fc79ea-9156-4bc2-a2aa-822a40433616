<svg width="990" height="743" viewBox="0 0 990 743" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_f_180_6033)">
    <ellipse cx="675.566" cy="610" rx="410" ry="475.566" transform="rotate(-90 675.566 610)" fill="url(#paint0_linear_180_6033)" fill-opacity="0.6"/>
  </g>
  <g filter="url(#filter1_f_180_6033)">
    <rect x="639.609" y="807" width="394" height="648.393" transform="rotate(-90 639.609 807)" fill="url(#paint1_linear_180_6033)" fill-opacity="0.6"/>
  </g>
  <defs>
    <filter id="filter0_f_180_6033" x="0" y="0" width="1351.13" height="1220" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_180_6033"/>
    </filter>
    <filter id="filter1_f_180_6033" x="439.609" y="213" width="1048.39" height="794" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_180_6033"/>
    </filter>
    <linearGradient id="paint0_linear_180_6033" x1="675.566" y1="134.434" x2="675.566" y2="1085.57" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFBE5" stop-opacity="0"/>
      <stop offset="0.355" stop-color="#15E3E3" stop-opacity="0.1"/>
      <stop offset="0.805" stop-color="#FFF800" stop-opacity="0.8"/>
    </linearGradient>
    <linearGradient id="paint1_linear_180_6033" x1="836.609" y1="807" x2="836.609" y2="1455.39" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0"/>
      <stop offset="1" stop-color="#A8F0F8"/>
    </linearGradient>
  </defs>
</svg>
