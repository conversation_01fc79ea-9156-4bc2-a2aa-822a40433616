<svg width="1220" height="1138" viewBox="0 0 1220 1138" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_f_180_8288)">
    <circle cx="610" cy="410" r="410" fill="url(#paint0_linear_180_8288)" fill-opacity="0.6"/>
  </g>
  <g filter="url(#filter1_f_180_8288)">
    <rect x="413" y="379" width="394" height="559" fill="url(#paint1_linear_180_8288)" fill-opacity="0.6"/>
  </g>
  <defs>
    <filter id="filter0_f_180_8288" x="0" y="-200" width="1220" height="1220" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_180_8288"/>
    </filter>
    <filter id="filter1_f_180_8288" x="213" y="179" width="794" height="959" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_180_8288"/>
    </filter>
    <linearGradient id="paint0_linear_180_8288" x1="610" y1="0" x2="610" y2="820" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFBE5" stop-opacity="0"/>
      <stop offset="0.355" stop-color="#15E3E3" stop-opacity="0.1"/>
      <stop offset="0.805" stop-color="#FFF800" stop-opacity="0.3"/>
    </linearGradient>
    <linearGradient id="paint1_linear_180_8288" x1="610" y1="379" x2="610" y2="938" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0"/>
      <stop offset="1" stop-color="#A8F0F8"/>
    </linearGradient>
  </defs>
</svg>
