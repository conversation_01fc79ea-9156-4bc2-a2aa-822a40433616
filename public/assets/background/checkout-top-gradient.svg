<svg width="928" height="979" viewBox="0 0 928 979" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_f_180_6036)">
    <ellipse cx="115.566" cy="369" rx="410" ry="475.566" transform="rotate(-90 115.566 369)" fill="url(#paint0_linear_180_6036)" fill-opacity="0.6"/>
  </g>
  <g filter="url(#filter1_f_180_6036)">
    <rect x="79.6094" y="566" width="394" height="648.393" transform="rotate(-90 79.6094 566)" fill="url(#paint1_linear_180_6036)" fill-opacity="0.6"/>
  </g>
  <defs>
    <filter id="filter0_f_180_6036" x="-560" y="-241" width="1351.13" height="1220" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_180_6036"/>
    </filter>
    <filter id="filter1_f_180_6036" x="-120.391" y="-28.0005" width="1048.39" height="794" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
      <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_180_6036"/>
    </filter>
    <linearGradient id="paint0_linear_180_6036" x1="115.566" y1="-106.566" x2="115.566" y2="844.566" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFFBE5" stop-opacity="0"/>
      <stop offset="0.355" stop-color="#15E3E3" stop-opacity="0.1"/>
      <stop offset="0.805" stop-color="#FFF800" stop-opacity="0.8"/>
    </linearGradient>
    <linearGradient id="paint1_linear_180_6036" x1="276.609" y1="566" x2="276.609" y2="1214.39" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" stop-opacity="0"/>
      <stop offset="1" stop-color="#A8F0F8"/>
    </linearGradient>
  </defs>
</svg>
